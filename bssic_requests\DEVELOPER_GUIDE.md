# Developer Guide - BSIC Requests Module

## Overview

This guide explains the new organized structure of the BSIC Requests module and how to work with it effectively.

## Architecture

The module uses a **mixin-based architecture** where functionality is split into focused, reusable components:

### Core Components

1. **Base Models** (`models/base/`): Core functionality shared across all request types
2. **Request Type Fields** (`models/request_types/`): Specific fields for each request type
3. **Wizards** (`models/wizards/`): Dialog boxes and temporary models
4. **Views** (`views/`): User interface definitions organized by purpose

### Mixin Pattern

Each request type extends the main model through mixins:

```python
class BSSICRequest(models.Model):
    _name = 'bssic.request'
    _inherit = [
        'mail.thread', 
        'mail.activity.mixin',
        'bssic.request.fields.mixin',           # Basic fields
        'bssic.request.password.reset.mixin',   # Password reset fields
        'bssic.request.usb.mixin',              # USB fields
        # ... other mixins
        'bssic.request.workflow.mixin',         # Workflow methods
        'bssic.request.validations.mixin',      # Validation rules
    ]
```

## Adding a New Request Type

### Step 1: Create Field Mixin

Create a new file in `models/request_types/new_request_fields.py`:

```python
from odoo import models, fields

class NewRequestFieldsMixin(models.AbstractModel):
    """Mixin containing new request type fields"""
    _name = 'bssic.request.new.request.mixin'
    _description = 'New Request Fields Mixin'

    # Add your specific fields here
    new_field_1 = fields.Char('Field 1', tracking=True)
    new_field_2 = fields.Selection([
        ('option1', 'Option 1'),
        ('option2', 'Option 2'),
    ], string='Field 2', tracking=True)
```

### Step 2: Update Main Model

Add your mixin to `models/base/request_main.py`:

```python
class BSSICRequest(models.Model):
    _inherit = [
        # ... existing mixins
        'bssic.request.new.request.mixin',  # Add your mixin
    ]
```

### Step 3: Update Request Type Model

Add a boolean field to `models/base/request_type.py`:

```python
show_new_request_fields = fields.Boolean('Show New Request Fields')
```

### Step 4: Update Field Computation

Add computation logic in `models/base/request_fields.py`:

```python
show_new_request_fields = fields.Boolean(
    string='Show New Request Fields',
    compute='_compute_show_fields'
)

@api.depends('request_type_id')
def _compute_show_fields(self):
    for record in self:
        # ... existing computations
        record.show_new_request_fields = record.request_type_id.show_new_request_fields if record.request_type_id else False
```

### Step 5: Create Views

Create `views/request_types/new_request_views.xml`:

```xml
<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Add your specific views here -->
    <record id="view_new_request_form" model="ir.ui.view">
        <field name="name">new.request.form</field>
        <field name="model">bssic.request</field>
        <field name="inherit_id" ref="view_bssic_request_form"/>
        <field name="arch" type="xml">
            <!-- Add your form fields -->
        </field>
    </record>
</odoo>
```

### Step 6: Add Validations (if needed)

Add validation methods to `models/base/request_validations.py`:

```python
@api.constrains('new_field_1', 'state', 'show_new_request_fields')
def _check_new_request_fields(self):
    for record in self:
        if record.show_new_request_fields and record.state != 'draft':
            if not record.new_field_1:
                raise UserError(_('Field 1 is required for New Request type.'))
```

### Step 7: Update Imports

Update `models/request_types/__init__.py`:

```python
from . import new_request_fields
```

### Step 8: Update Manifest

Add your view file to `__manifest__.py`:

```python
'views/request_types/new_request_views.xml',
```

## Best Practices

### 1. File Organization
- Keep files small and focused (< 300 lines)
- Use descriptive names
- Group related functionality together

### 2. Naming Conventions
- Mixins: `bssic.request.{type}.mixin`
- Fields: `{type}_{field_name}`
- Views: `{type}_request_views.xml`

### 3. Field Definitions
- Always add `tracking=True` for important fields
- Use appropriate field types
- Add help text for complex fields

### 4. Validations
- Group related validations in single methods
- Use clear error messages
- Consider user experience

### 5. Views
- Inherit from base views when possible
- Use consistent styling
- Group related fields together

## Debugging Tips

### 1. Check Imports
Ensure all new files are properly imported in `__init__.py` files.

### 2. Restart Odoo
After adding new mixins, restart Odoo and update the module.

### 3. Check Logs
Monitor Odoo logs for import errors or missing dependencies.

### 4. Test Incrementally
Test each step before moving to the next.

## Migration Notes

When upgrading from the old structure:
1. All functionality is preserved
2. Database structure remains unchanged
3. Existing data is not affected
4. Views and workflows work as before

The refactoring only affects code organization, not functionality.
