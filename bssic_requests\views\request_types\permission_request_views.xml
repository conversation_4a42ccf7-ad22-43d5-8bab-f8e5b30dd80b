<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_bssic_permission_request_form" model="ir.ui.view">
        <field name="name">bssic.permission.request.form</field>
        <field name="model">bssic.permission.request</field>
        <field name="arch" type="xml">
            <form string="Permission Request">
                <header>
                    <button name="action_submit" string="Submit" type="object" class="oe_highlight" states="draft"/>
                    <button name="action_direct_manager_approve" string="Approve" type="object" class="oe_highlight" states="submitted" groups="bssic_requests.group_bssic_manager"/>
                    <button name="action_dept_manager_approve" string="Approve" type="object" class="oe_highlight" states="direct_manager_approval" groups="bssic_requests.group_bssic_dept_manager"/>
                    <button name="action_it_manager_approve" string="Approve" type="object" class="oe_highlight" states="dept_manager_approval" groups="bssic_requests.group_bssic_it_manager"/>
                    <button name="action_assign" string="Assign to IT Staff" type="object" class="oe_highlight" states="it_manager_approval" groups="bssic_requests.group_bssic_it_manager"/>
                    <button name="action_start_progress" string="Start Progress" type="object" class="oe_highlight" states="assigned" groups="bssic_requests.group_bssic_it_staff"/>
                    <button name="action_complete" string="Complete" type="object" class="oe_highlight" states="in_progress" groups="bssic_requests.group_bssic_it_staff"/>
                    <button name="action_reject" string="Reject" type="object" class="btn-danger" states="submitted,direct_manager_approval,dept_manager_approval,it_manager_approval"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,submitted,direct_manager_approval,dept_manager_approval,it_manager_approval,assigned,in_progress,completed"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="employee_id" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="employee_number"/>
                            <field name="department_id"/>
                            <field name="job_title" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                        </group>
                        <group>
                            <field name="request_date" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="user_name" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="assigned_to" options="{'no_create': True}"
                                   attrs="{'invisible': [('state', 'not in', ['assigned', 'in_progress', 'completed', 'rejected'])],
                                          'readonly': [('state', 'not in', ['assigned'])],
                                          'required': [('state', '=', 'assigned')]}"
                                   domain="[('department_id.name', 'ilike', 'IT')]"
                                   groups="bssic_requests.group_bssic_it_manager"/>
                        </group>
                    </group>

                    <group string="Permission Details">
                        <group>
                            <field name="permission_type" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="validity_from" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="validity_to" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                        </group>
                    </group>

                    <notebook>
                        <page string="Department Permissions">
                            <div class="alert alert-info" role="alert" style="margin-bottom: 10px;">
                                <strong>ملاحظة:</strong> يجب اختيار قسم واحد على الأقل من الأقسام التالية قبل إرسال الطلب.
                            </div>
                            <group>
                                <group string="Accounting Department">
                                    <field name="accounting_dept" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="accounting_level" attrs="{'readonly': [('state', '!=', 'draft')], 'invisible': [('accounting_dept', '=', False)], 'required': [('accounting_dept', '=', True)]}"/>
                                    <field name="accounting_group" attrs="{'readonly': [('state', '!=', 'draft')], 'invisible': [('accounting_dept', '=', False)]}"/>
                                </group>
                                <group string="Internal Auditing">
                                    <field name="internal_audit" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="internal_audit_level" attrs="{'readonly': [('state', '!=', 'draft')], 'invisible': [('internal_audit', '=', False)], 'required': [('internal_audit', '=', True)]}"/>
                                    <field name="internal_audit_group" attrs="{'readonly': [('state', '!=', 'draft')], 'invisible': [('internal_audit', '=', False)]}"/>
                                </group>
                            </group>

                            <group>
                                <group string="Risk">
                                    <field name="risk_dept" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="risk_level" attrs="{'readonly': [('state', '!=', 'draft')], 'invisible': [('risk_dept', '=', False)], 'required': [('risk_dept', '=', True)]}"/>
                                    <field name="risk_group" attrs="{'readonly': [('state', '!=', 'draft')], 'invisible': [('risk_dept', '=', False)]}"/>
                                </group>
                                <group string="Back Office - Credits">
                                    <field name="back_office_credits" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="back_office_credits_level" attrs="{'readonly': [('state', '!=', 'draft')], 'invisible': [('back_office_credits', '=', False)], 'required': [('back_office_credits', '=', True)]}"/>
                                    <field name="back_office_credits_group" attrs="{'readonly': [('state', '!=', 'draft')], 'invisible': [('back_office_credits', '=', False)]}"/>
                                </group>
                            </group>

                            <group>
                                <group string="Back Office - Deposits">
                                    <field name="back_office_deposits" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="back_office_deposits_level" attrs="{'readonly': [('state', '!=', 'draft')], 'invisible': [('back_office_deposits', '=', False)], 'required': [('back_office_deposits', '=', True)]}"/>
                                    <field name="back_office_deposits_group" attrs="{'readonly': [('state', '!=', 'draft')], 'invisible': [('back_office_deposits', '=', False)]}"/>
                                </group>
                                <group string="Operations Department">
                                    <field name="operations_dept" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="operations_level" attrs="{'readonly': [('state', '!=', 'draft')], 'invisible': [('operations_dept', '=', False)], 'required': [('operations_dept', '=', True)]}"/>
                                    <field name="operations_group" attrs="{'readonly': [('state', '!=', 'draft')], 'invisible': [('operations_dept', '=', False)]}"/>
                                </group>
                            </group>

                            <group>
                                <group string="Forex Exchange">
                                    <field name="forex_exchange" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="forex_level" attrs="{'readonly': [('state', '!=', 'draft')], 'invisible': [('forex_exchange', '=', False)], 'required': [('forex_exchange', '=', True)]}"/>
                                    <field name="forex_group" attrs="{'readonly': [('state', '!=', 'draft')], 'invisible': [('forex_exchange', '=', False)]}"/>
                                </group>
                                <group string="Banking Operations">
                                    <field name="banking_operations" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="banking_level" attrs="{'readonly': [('state', '!=', 'draft')], 'invisible': [('banking_operations', '=', False)], 'required': [('banking_operations', '=', True)]}"/>
                                    <field name="banking_group" attrs="{'readonly': [('state', '!=', 'draft')], 'invisible': [('banking_operations', '=', False)]}"/>
                                </group>
                            </group>

                            <group>
                                <group string="Personnel &amp; Admin">
                                    <field name="personnel_admin" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="personnel_level" attrs="{'readonly': [('state', '!=', 'draft')], 'invisible': [('personnel_admin', '=', False)], 'required': [('personnel_admin', '=', True)]}"/>
                                    <field name="personnel_group" attrs="{'readonly': [('state', '!=', 'draft')], 'invisible': [('personnel_admin', '=', False)]}"/>
                                </group>
                                <group string="Swift">
                                    <field name="swift" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="swift_level" attrs="{'readonly': [('state', '!=', 'draft')], 'invisible': [('swift', '=', False)], 'required': [('swift', '=', True)]}"/>
                                    <field name="swift_group" attrs="{'readonly': [('state', '!=', 'draft')], 'invisible': [('swift', '=', False)]}"/>
                                </group>
                            </group>
                        </page>

                        <page string="Transaction Limits">
                            <group>
                                <group>
                                    <field name="transaction_amount_limit" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                </group>
                                <group>
                                    <field name="auth_limit" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                </group>
                                <group>
                                    <field name="max_amount_limit" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                </group>
                            </group>
                        </page>

                        <page string="سجل الحركات" name="activity_log" attrs="{'invisible': [('id', '=', False)]}">
                            <field name="activity_log_ids" readonly="1">
                                <tree string="Activity Log" create="false" edit="false" delete="false">
                                    <field name="activity_date"/>
                                    <field name="activity_type"/>
                                    <field name="user_id"/>
                                    <field name="employee_id"/>
                                    <field name="old_state"/>
                                    <field name="new_state"/>
                                    <field name="notes"/>
                                    <field name="rejection_reason" attrs="{'invisible': [('rejection_reason', '=', False)]}"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids" widget="mail_followers"/>
                    <field name="activity_ids" widget="mail_activity"/>
                    <field name="message_ids" widget="mail_thread"/>
                </div>
            </form>
        </field>
    </record>

    <record id="view_bssic_permission_request_tree" model="ir.ui.view">
        <field name="name">bssic.permission.request.tree</field>
        <field name="model">bssic.permission.request</field>
        <field name="arch" type="xml">
            <tree string="Permission Requests" decoration-info="state == 'draft'" decoration-muted="state == 'rejected'" decoration-success="state == 'completed'">
                <field name="name"/>
                <field name="employee_id"/>
                <field name="department_id"/>
                <field name="request_date"/>
                <field name="permission_type"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <record id="action_bssic_permission_request_model" model="ir.actions.act_window">
        <field name="name">Permission Requests (Separate Model)</field>
        <field name="res_model">bssic.permission.request</field>
        <field name="view_mode">tree,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new permission request
            </p>
        </field>
    </record>

    <!-- Add to menu -->
    <menuitem id="menu_bssic_permission_request_model"
              name="Permission Requests (Separate Model)"
              parent="menu_bssic_request"
              action="action_bssic_permission_request_model"
              sequence="80"/>
</odoo>