from odoo import models, fields, api, _

class RequestFieldsMixin(models.AbstractModel):
    """Mixin containing all field definitions for BSSIC requests"""
    _name = 'bssic.request.fields.mixin'
    _description = 'BSSIC Request Fields Mixin'

    # Basic fields
    name = fields.Char('Request Reference', required=True, copy=False, readonly=True,
                       default=lambda self: _('New'))
    employee_id = fields.Many2one('hr.employee', string='Employee', required=True,
                                   tracking=True, ondelete="restrict")
    employee_number = fields.Char(string='Employee Number (ID)', tracking=True,
                                help="Enter employee ID number to automatically fetch employee details")
    department_id = fields.Many2one(related='employee_id.department_id',
                                   string='Department', store=True, readonly=True)
    job_id = fields.Many2one(related='employee_id.job_id', string='Job Position',
                            store=True, readonly=True)
    request_type_id = fields.Many2one('bssic.request.type', string='Request Type',
                                     required=True, tracking=True)
    request_type_code = fields.Char('Request Type Code', tracking=True)
    request_date = fields.Date('Request Date', default=fields.Date.context_today,
                              required=True, tracking=True)
    description = fields.Text('Description', tracking=True)

    # State and workflow fields
    state = fields.Selection([
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('direct_manager', 'Direct Manager Approval'),
        ('audit_manager', 'Audit Manager Approval'),
        ('it_manager', 'IT Manager Approval'),
        ('assigned', 'Assigned to IT Staff'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('rejected', 'Rejected'),
    ], string='Status', default='draft', tracking=True)

    assigned_to = fields.Many2one('hr.employee', string='Assigned To',
                                 tracking=True, ondelete="set null",
                                 domain="[('department_id.name', 'ilike', 'IT')]")
    rejection_reason = fields.Text('Rejection Reason', tracking=True)
    completion_notes = fields.Text('Completion Notes', tracking=True)

    # Activity Log
    activity_log_ids = fields.One2many('bssic.request.activity.log', 'request_id', string='Activity Log')

    # Computed fields for showing different field groups
    show_password_fields = fields.Boolean(
        string='Show Password Fields',
        compute='_compute_show_fields'
    )
    show_usb_fields = fields.Boolean(
        string='Show USB Fields',
        compute='_compute_show_fields'
    )
    show_extension_fields = fields.Boolean(
        string='Show Extension Fields',
        compute='_compute_show_fields'
    )
    show_permission_fields = fields.Boolean(
        string='Show Permission Fields',
        compute='_compute_show_fields'
    )
    show_email_fields = fields.Boolean(
        string='Show Email Fields',
        compute='_compute_show_fields'
    )
    show_technical_fields = fields.Boolean(
        string='Show Technical Fields',
        compute='_compute_show_fields'
    )
    show_authorization_delegation_fields = fields.Boolean(
        string='Show Authorization Delegation Fields',
        compute='_compute_show_fields'
    )
    show_free_entry_fields = fields.Boolean(
        string='Show Free Entry Fields',
        compute='_compute_show_fields'
    )

    # Technical request fields
    is_technical = fields.Boolean(string='Is Technical Request', default=False)
    is_manager = fields.Boolean(string='Is Manager', compute='_compute_is_manager', store=False)

    @api.depends('request_type_id')
    def _compute_show_fields(self):
        for record in self:
            record.show_password_fields = record.request_type_id.show_password_fields if record.request_type_id else False
            record.show_usb_fields = record.request_type_id.show_usb_fields if record.request_type_id else False
            record.show_extension_fields = record.request_type_id.show_extension_fields if record.request_type_id else False
            record.show_permission_fields = record.request_type_id.show_permission_fields if record.request_type_id else False
            record.show_email_fields = record.request_type_id.show_email_fields if record.request_type_id else False
            record.show_authorization_delegation_fields = record.request_type_id.show_authorization_delegation_fields if record.request_type_id else False
            record.show_free_entry_fields = record.request_type_id.show_free_entry_fields if record.request_type_id else False
            is_tech = record.request_type_id.code == 'technical' if record.request_type_id else False
            record.show_technical_fields = is_tech
            record.is_technical = is_tech

    @api.depends()
    def _compute_is_manager(self):
        """Check if the current user has direct manager permissions"""
        is_manager = self.env.user.has_group('bssic_requests.group_bssic_direct_manager')
        for record in self:
            record.is_manager = is_manager
