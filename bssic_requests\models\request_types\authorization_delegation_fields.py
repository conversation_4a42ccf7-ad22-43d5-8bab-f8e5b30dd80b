from odoo import models, fields

class AuthorizationDelegationFieldsMixin(models.AbstractModel):
    """Mixin containing authorization delegation request fields"""
    _name = 'bssic.request.authorization.delegation.mixin'
    _description = 'Authorization Delegation Request Fields Mixin'

    # For authorization delegation request
    ceiling_reason = fields.Text(string='Reason for Ceiling Increase', tracking=True)
    delegation_details = fields.Text(string='Details', tracking=True)
    delegation_max_amount = fields.Float(string='Max. Amount', tracking=True)
    delegation_auth_limit = fields.Float(string='Auth O.D. Limit', tracking=True)
    delegation_from_date = fields.Date(string='From Date', tracking=True)
    delegation_to_date = fields.Date(string='To Date', tracking=True)
