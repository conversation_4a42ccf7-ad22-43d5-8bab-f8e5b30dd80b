{
    'name': 'BSIC Requests Management',
    'version': '1.0.2',
    'summary': 'Manage IT and other requests with approval workflow',
    'description': """
        This module manages various types of requests with a multi-stage approval workflow:
        - Password reset requests
        - Device usage extension requests
        - USB storage usage requests
        - And more...
    """,
    'category': 'Human Resources',
    'author': 'BSIC',
    'depends': ['base', 'hr', 'mail'],
    # Find this section in your manifest file
    'data': [
        # Security
        'security/security.xml',
        'security/ir.model.access.csv',

        # Data
        'data/sequence.xml',
        'data/request_type_data.xml',
        'data/technical_category_data.xml',
        'data/stationery_item_data.xml',

        # Base views
        'views/base/request_views.xml',
        'views/base/request_type_views.xml',
        'views/base/technical_category_views.xml',
        'views/base/request_activity_log_views.xml',

        # Request type specific views
        'views/request_types/password_reset_request_views.xml',
        'views/request_types/usb_request_views.xml',
        'views/request_types/extension_request_views.xml',
        'views/request_types/permission_request_views.xml',
        'views/request_types/permission_request_views_new.xml',
        'views/request_types/email_request_views.xml',
        'views/request_types/technical_request_views.xml',
        'views/request_types/technical_request_views_new.xml',
        'views/request_types/authorization_delegation_request_views.xml',
        'views/request_types/free_entry_request_views.xml',

        # Wizard views
        'views/wizards/receipt_confirmation_wizard_view.xml',

        # Actions (must be loaded before menus)
        'views/request_type_action.xml',
        'views/stationery_item_action.xml',
        'views/stationery_request_views.xml',

        # Menu views (loaded after actions)
        'views/menus/menu_views.xml',
        'views/menus/technical_support_menu.xml',

        # Uncomment the line below if you want to use the request_menu.xml file
        # 'views/request_menu.xml',
    ],
    'demo': [],
    'installable': True,
    'application': True,
    'auto_install': False,
}