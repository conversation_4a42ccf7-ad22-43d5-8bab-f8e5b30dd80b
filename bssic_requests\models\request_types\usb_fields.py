from odoo import models, fields

class USBFieldsMixin(models.AbstractModel):
    """Mixin containing USB request fields"""
    _name = 'bssic.request.usb.mixin'
    _description = 'USB Request Fields Mixin'

    # For USB request
    usb_purpose = fields.Char('Purpose of USB Usage', tracking=True)
    usb_duration = fields.Char('Required Duration', tracking=True)
    data_type = fields.Char('Type of Data to Transfer', tracking=True)
