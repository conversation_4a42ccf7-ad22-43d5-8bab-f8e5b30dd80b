from odoo import models, fields

class PasswordResetFieldsMixin(models.AbstractModel):
    """Mixin containing password reset request fields"""
    _name = 'bssic.request.password.reset.mixin'
    _description = 'Password Reset Request Fields Mixin'

    # For password reset request
    username = fields.Char('Username', tracking=True)
    device_type = fields.Selection([
        ('internet', 'Internet'),
        ('system', 'System'),
        ('swift', 'Swift'),
        ('other', 'Other')
    ], string='Device Type', tracking=True)
    request_reason = fields.Selection([
        ('password_reset', 'Password Reset (Forgotten/Unable to login)'),
        ('account_reactivation', 'Account Reactivation (Account/Device locked)')
    ], string='Request Reason', tracking=True)
