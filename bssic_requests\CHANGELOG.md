# BSIC Requests Management - Changelog

## Version 1.0.2 (2025-01-06)

### 🔧 Major Code Organization Refactoring

#### Changed - Code Organization
- **Major refactoring**: Reorganized entire codebase for better maintainability
- Split large `request.py` file (952 lines) into smaller, focused modules:
  - `base/request_fields.py` - Basic field definitions
  - `base/request_workflow.py` - Workflow and approval logic
  - `base/request_validations.py` - Validation constraints
  - `base/request_helpers.py` - Helper methods
  - `base/request_onchange.py` - Onchange methods
  - `base/request_main.py` - Main model combining all mixins
- Created request type specific field modules in `request_types/` directory
- Organized views into logical directories:
  - `views/base/` - Core views
  - `views/request_types/` - Request type specific views
  - `views/wizards/` - Wizard views
  - `views/menus/` - Menu definitions
- Consolidated security files and removed duplicates
- Updated `__manifest__.py` to reflect new organized structure

#### Benefits
- **Improved maintainability**: Smaller, focused files are easier to understand and modify
- **Better separation of concerns**: Each file has a specific responsibility
- **Enhanced readability**: Code is now logically grouped and well-organized
- **Easier debugging**: Issues can be traced to specific functional areas
- **Future-proof**: New request types can be easily added without affecting existing code

#### Technical Details
- No functional changes - all existing features preserved
- All workflows and validations remain intact
- Backward compatibility maintained
- Uses mixin pattern for better code reuse

## Version 1.0.1 (2024-12-19)

### ✨ New Features
- **Enhanced Permission Level Fields**: Converted all department permission level fields from text input to dropdown selection fields

### 🔧 Changes Made

#### Permission Level Fields Updated:
The following fields in Permission Requests now use dropdown selection instead of free text input:

1. **Accounting Level** (`accounting_level`)
2. **Risk Level** (`risk_level`)
3. **Deposits Level** (`back_office_deposits_level`)
4. **Forex Level** (`forex_level`)
5. **Personnel Level** (`personnel_level`)
6. **Internal Audit Level** (`internal_audit_level`)
7. **Credits Level** (`back_office_credits_level`)
8. **Operations Level** (`operations_level`)
9. **Banking Level** (`banking_level`)
10. **Swift Level** (`swift_level`)

#### Available Selection Options:
Each level field now offers the following standardized options:
- **User** - Basic user level access
- **Clark** - Clerk level access
- **Verifier1** - First level verification access
- **Verifier2** - Second level verification access
- **Authorizer** - Authorization level access

### 🔄 Migration Support
- Added automatic migration script to convert existing text values to selection values
- Existing data will be preserved and mapped to appropriate selection values
- Unknown values will be cleared and can be re-selected from the dropdown

### 📁 Files Modified:
- `models/request.py` - Updated main request model
- `models/permission_request.py` - Updated permission request model
- `__manifest__.py` - Version bump to 1.0.1
- `migrations/1.0.1/post-migration.py` - Migration script for existing data

### 🎯 Benefits:
- **Data Consistency**: Standardized permission levels across all requests
- **User Experience**: Easier selection with dropdown instead of typing
- **Data Validation**: Prevents typos and ensures valid permission levels
- **Reporting**: Better data quality for analytics and reporting

### 🔧 Technical Details:
- All level fields converted from `fields.Char` to `fields.Selection`
- Maintains backward compatibility through migration script
- No changes required to existing views - they will automatically show dropdowns
- Tracking enabled for all modified fields

---

## Version 1.0.0 (Previous)
- Initial release of BSIC Requests Management module
- Multi-stage approval workflow
- Various request types (Password Reset, USB, Device Extension, etc.)
- Role-based permissions and security groups
