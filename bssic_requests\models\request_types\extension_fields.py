from odoo import models, fields

class ExtensionFieldsMixin(models.AbstractModel):
    """Mixin containing extension request fields"""
    _name = 'bssic.request.extension.mixin'
    _description = 'Extension Request Fields Mixin'

    # For device extension request
    extension_duration = fields.Char('Required Extension Period', tracking=True)
    extension_reason = fields.Text('Reason for Extension', tracking=True)
