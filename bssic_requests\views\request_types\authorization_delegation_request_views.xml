<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Authorization Delegation Request Form View -->
        <record id="view_authorization_delegation_request_form" model="ir.ui.view">
            <field name="name">authorization.delegation.request.form</field>
            <field name="model">bssic.request</field>
            <field name="arch" type="xml">
                <form string="Authorization Delegation Request">
                    <header>
                        <button name="action_submit" string="Submit" type="object"
                                class="oe_highlight" states="draft"/>
                        <button name="action_approve_direct_manager" string="Approve (Direct Manager)"
                                type="object" class="oe_highlight" states="direct_manager"
                                groups="bssic_requests.group_bssic_direct_manager"/>
                        <button name="action_approve_audit_manager" string="Approve (Audit Manager)"
                                type="object" class="oe_highlight" states="audit_manager"
                                groups="bssic_requests.group_bssic_audit_manager"/>
                        <button name="action_approve_it_manager" string="Approve (IT Manager)"
                                type="object" class="oe_highlight" states="it_manager"
                                groups="bssic_requests.group_bssic_it_manager"/>
                        <button name="action_assign" string="Assign to IT Staff"
                                type="object" class="oe_highlight" states="assigned"
                                groups="bssic_requests.group_bssic_it_manager"/>
                        <button name="action_complete" string="Mark as Completed"
                                type="object" class="oe_highlight" states="in_progress"
                                groups="bssic_requests.group_bssic_it_staff"/>
                        <button name="action_reject" string="Reject" type="object"
                                class="oe_reject" states="direct_manager,audit_manager,it_manager"
                                groups="bssic_requests.group_bssic_direct_manager,bssic_requests.group_bssic_audit_manager,bssic_requests.group_bssic_it_manager"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,submitted,direct_manager,audit_manager,it_manager,assigned,in_progress,completed"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="name" readonly="1"/>
                            </h1>
                        </div>
                        <group>
                            <group>
                                <field name="request_type_id" readonly="1"/>
                                <field name="employee_number" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="employee_id" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="department_id"/>
                                <field name="job_id"/>
                            </group>
                            <group>
                                <field name="request_date" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="assigned_to" attrs="{'readonly': [('state', 'not in', ['assigned'])]}"
                                       groups="bssic_requests.group_bssic_it_manager"/>
                            </group>
                        </group>

                        <!-- Authorization Delegation Specific Fields -->
                        <group string="Authorization Delegation Details" attrs="{'invisible': [('show_authorization_delegation_fields', '=', False)]}">
                            <group>
                                <field name="ceiling_reason" string="Reason for Ceiling Increase" required="1" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="delegation_details" string="Details" required="1" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="delegation_from_date" string="From Date" required="1" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="delegation_to_date" string="To Date" required="1" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            </group>
                            <group>
                                <field name="delegation_max_amount" string="Max. Amount" required="1" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                <field name="delegation_auth_limit" string="Auth O.D. Limit" required="1" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            </group>
                        </group>

                        <group string="Description">
                            <field name="description" nolabel="1" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                        </group>

                        <!-- Rejection and Completion Notes -->
                        <group string="Notes" attrs="{'invisible': [('state', 'in', ['draft', 'submitted', 'direct_manager', 'audit_manager', 'it_manager', 'assigned'])]}">
                            <field name="rejection_reason" attrs="{'invisible': [('state', '!=', 'rejected')]}"/>
                            <field name="completion_notes" attrs="{'invisible': [('state', 'not in', ['in_progress', 'completed'])], 'readonly': [('state', '=', 'completed')]}"/>
                        </group>

                        <!-- Hidden fields for computation -->
                        <field name="show_authorization_delegation_fields" invisible="1"/>
                        <field name="request_type_code" invisible="1"/>
                    </sheet>
                    <div class="oe_chatter">
                        <field name="message_follower_ids" widget="mail_followers"/>
                        <field name="activity_ids" widget="mail_activity"/>
                        <field name="message_ids" widget="mail_thread"/>
                    </div>
                </form>
            </field>
        </record>

        <!-- Authorization Delegation Request Tree View -->
        <record id="view_authorization_delegation_request_tree" model="ir.ui.view">
            <field name="name">authorization.delegation.request.tree</field>
            <field name="model">bssic.request</field>
            <field name="arch" type="xml">
                <tree string="Authorization Delegation Requests">
                    <field name="name"/>
                    <field name="employee_id"/>
                    <field name="department_id"/>
                    <field name="request_date"/>
                    <field name="delegation_max_amount"/>
                    <field name="delegation_auth_limit"/>
                    <field name="delegation_from_date"/>
                    <field name="delegation_to_date"/>
                    <field name="state" decoration-info="state=='draft'"
                           decoration-warning="state in ['submitted','direct_manager','audit_manager','it_manager']"
                           decoration-success="state=='completed'"
                           decoration-danger="state=='rejected'"/>
                </tree>
            </field>
        </record>

        <!-- Authorization Delegation Request Search View -->
        <record id="view_authorization_delegation_request_search" model="ir.ui.view">
            <field name="name">authorization.delegation.request.search</field>
            <field name="model">bssic.request</field>
            <field name="arch" type="xml">
                <search string="Authorization Delegation Requests">
                    <field name="name"/>
                    <field name="employee_id"/>
                    <field name="department_id"/>
                    <field name="ceiling_reason"/>
                    <field name="delegation_details"/>
                    <separator/>
                    <filter string="My Requests" name="my_requests"
                            domain="[('employee_id.user_id', '=', uid)]"/>
                    <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                    <filter string="Pending Approval" name="pending"
                            domain="[('state', 'in', ['submitted', 'direct_manager', 'audit_manager', 'it_manager'])]"/>
                    <filter string="In Progress" name="in_progress" domain="[('state', '=', 'in_progress')]"/>
                    <filter string="Completed" name="completed" domain="[('state', '=', 'completed')]"/>
                    <filter string="Rejected" name="rejected" domain="[('state', '=', 'rejected')]"/>
                    <separator/>
                    <group expand="0" string="Group By">
                        <filter string="Employee" name="group_employee" context="{'group_by': 'employee_id'}"/>
                        <filter string="Department" name="group_department" context="{'group_by': 'department_id'}"/>
                        <filter string="Status" name="group_state" context="{'group_by': 'state'}"/>
                        <filter string="Request Date" name="group_date" context="{'group_by': 'request_date'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Authorization Delegation Request Action -->
        <record id="action_authorization_delegation_request" model="ir.actions.act_window">
            <field name="name">Authorization Delegation Requests</field>
            <field name="res_model">bssic.request</field>
            <field name="view_mode">tree,form</field>
            <field name="domain">[('request_type_id.code', '=', 'authorization_delegation')]</field>
            <field name="context">{
                'default_request_type_code': 'authorization_delegation',
                'search_default_my_requests': 1
            }</field>
            <field name="view_ids" eval="[(5, 0, 0),
                (0, 0, {'view_mode': 'tree', 'view_id': ref('view_authorization_delegation_request_tree')}),
                (0, 0, {'view_mode': 'form', 'view_id': ref('view_authorization_delegation_request_form')})]"/>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create your first Authorization Delegation Request!
                </p>
                <p>
                    Submit requests for authorization delegation in user ceiling limits.
                </p>
            </field>
        </record>

        <!-- Menu Item for Authorization Delegation Requests -->
        <menuitem id="menu_authorization_delegation_request"
                  name="Authorization Delegation"
                  parent="menu_bssic_request"
                  action="action_authorization_delegation_request"
                  sequence="60"/>

    </data>
</odoo>
