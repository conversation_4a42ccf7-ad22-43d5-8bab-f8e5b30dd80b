<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Technical Request Form View -->
    <record id="view_bssic_technical_request_form" model="ir.ui.view">
        <field name="name">bssic.technical.request.form</field>
        <field name="model">bssic.request</field>
        <field name="arch" type="xml">
            <form string="Technical Request">
                <header>
                    <button name="action_submit" string="Submit" type="object" class="oe_highlight" states="draft"/>
                    <button name="action_approve_direct_manager" string="Approve (Direct Manager)" type="object" class="oe_highlight"
                            states="direct_manager" groups="bssic_requests.group_bssic_direct_manager"/>
                    <button name="action_approve_it_manager" string="Approve (IT Manager)" type="object" class="oe_highlight"
                            states="it_manager" groups="bssic_requests.group_bssic_it_manager"/>
                    <button name="action_assign" string="Assign to IT Staff" type="object" class="oe_highlight"
                            states="assigned" groups="bssic_requests.group_bssic_it_manager"/>
                    <button name="action_complete" string="Mark as Completed" type="object" class="oe_highlight"
                            states="in_progress" groups="bssic_requests.group_bssic_it_staff"/>
                    <button name="action_reject" string="Reject" type="object" class="btn-danger"
                            states="direct_manager,it_manager,assigned,in_progress"/>
                    <field name="is_technical" invisible="1"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,submitted,direct_manager,it_manager,assigned,in_progress,completed"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                        <h3>
                            <field name="request_type_id" readonly="1" options="{'no_open': True}"/>
                        </h3>
                    </div>
                    <group>
                        <group>
                            <field name="employee_number" readonly="1"/>
                            <field name="employee_id" options="{'no_create': True}" readonly="1"/>
                            <field name="department_id" readonly="1"/>
                            <field name="job_id" readonly="1"/>
                            <field name="request_type_code" invisible="1"/>
                            <field name="show_technical_fields" invisible="1"/>
                        </group>
                        <group>
                            <field name="request_date" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="assigned_to" options="{'no_create': True}"
                                   attrs="{'invisible': [('state', 'not in', ['assigned', 'in_progress', 'completed', 'rejected'])],
                                          'readonly': [('state', 'not in', ['assigned'])],
                                          'required': [('state', '=', 'assigned')]}"
                                   context="{'it_staff_only': True}"
                                   groups="bssic_requests.group_bssic_it_manager"/>
                        </group>
                    </group>

                    <notebook>
                        <page string="Request Details" name="request_details">
                            <group>
                                <group>
                                    <field name="request_nature" widget="radio" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="technical_category_id" attrs="{'required': [('is_technical', '=', True)], 'readonly': [('state', '!=', 'draft')]}"/>
                                </group>
                                <group>
                                    <field name="technical_subcategory_id" attrs="{'required': [('is_technical', '=', True)], 'readonly': [('state', '!=', 'draft')]}"/>
                                    <field name="priority" widget="priority" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                                </group>
                            </group>
                            <group>
                                <field name="description" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            </group>
                        </page>

                        <page string="Notes" name="notes">
                            <group>
                                <field name="rejection_reason" attrs="{'invisible': [('state', '!=', 'rejected')]}" readonly="1"/>
                                <field name="completion_notes" attrs="{'invisible': [('state', 'not in', ['in_progress', 'completed'])], 'readonly': [('state', '=', 'completed')]}"/>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>

    <!-- Technical Request Tree View -->
    <record id="view_bssic_technical_request_tree" model="ir.ui.view">
        <field name="name">bssic.technical.request.tree</field>
        <field name="model">bssic.request</field>
        <field name="arch" type="xml">
            <tree string="Technical Requests" decoration-info="state == 'draft'" decoration-muted="state == 'rejected'" decoration-success="state == 'completed'">
                <field name="name"/>
                <field name="employee_id"/>
                <field name="department_id"/>
                <field name="technical_category_id"/>
                <field name="priority"/>
                <field name="request_date"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <!-- Update the technical request actions to use the new views -->
    <record id="action_technical_support_requests" model="ir.actions.act_window">
        <field name="name">Technical Requests</field>
        <field name="res_model">bssic.request</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('request_type_id.code', '=', 'technical')]</field>
        <field name="context">{'default_request_type_code': 'technical', 'default_is_technical': True, 'default_request_nature': 'technical', 'default_name': 'Technical Request'}</field>
        <field name="view_id" ref="view_bssic_technical_request_form"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new technical request
            </p>
        </field>
    </record>

    <record id="action_my_technical_requests" model="ir.actions.act_window">
        <field name="name">My Technical Requests</field>
        <field name="res_model">bssic.request</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('request_type_id.code', '=', 'technical'), ('employee_id.user_id', '=', uid)]</field>
        <field name="context">{'default_request_type_code': 'technical', 'default_is_technical': True, 'default_request_nature': 'technical', 'default_name': 'Technical Request'}</field>
        <field name="view_id" ref="view_bssic_technical_request_form"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new technical request
            </p>
        </field>
    </record>

    <record id="action_all_technical_requests" model="ir.actions.act_window">
        <field name="name">All Technical Requests</field>
        <field name="res_model">bssic.request</field>
        <field name="view_mode">tree,form</field>
        <field name="domain">[('request_type_id.code', '=', 'technical')]</field>
        <field name="context">{'default_request_type_code': 'technical', 'default_is_technical': True, 'default_request_nature': 'technical', 'default_name': 'Technical Request'}</field>
        <field name="view_id" ref="view_bssic_technical_request_form"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                View all technical requests
            </p>
        </field>
    </record>
</odoo>
