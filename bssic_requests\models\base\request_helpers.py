from odoo import models, api, _
from lxml import etree

class RequestHelpersMixin(models.AbstractModel):
    """Mixin containing helper methods for BSSIC requests"""
    _name = 'bssic.request.helpers.mixin'
    _description = 'BSSIC Request Helpers Mixin'

    def _get_it_staff_domain(self):
        """Get domain for IT staff employees"""
        # Get the IT Staff group ID
        it_staff_group_id = self.env.ref('bssic_requests.group_bssic_it_staff').id

        # Find users who belong to the IT Staff group
        it_staff_users = self.env['res.users'].search([('groups_id', 'in', [it_staff_group_id])])

        # Find employees linked to these users
        it_staff_employees = self.env['hr.employee'].search([('user_id', 'in', it_staff_users.ids)])

        # Also include employees from IT department
        it_dept_employees = self.env['hr.employee'].search([('department_id.name', 'ilike', 'IT')])

        # Combine both sets of employees
        all_it_employees = it_staff_employees | it_dept_employees

        return [('id', 'in', all_it_employees.ids)]

    @api.model
    def _get_employee_domain(self):
        """Get domain for employee selection based on user permissions"""
        # Get current user's employee record
        current_user = self.env.user
        employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)

        # Check if user is a manager
        is_manager = self.env.user.has_group('bssic_requests.group_bssic_direct_manager')

        if is_manager and employee:
            # Get all employees in the same department that report to this manager
            department_employees = self.env['hr.employee'].search([
                '|',
                ('id', '=', employee.id),  # Include the manager
                '&',
                ('department_id', '=', employee.department_id.id),  # Same department
                ('parent_id', '=', employee.id)  # Reports to this manager
            ])
            return [('id', 'in', department_employees.ids)]
        else:
            # Regular employee can only select themselves
            if employee:
                return [('id', '=', employee.id)]
            else:
                return [('id', '=', -1)]  # No employee should match this domain

    @api.model
    def fields_view_get(self, view_id=None, view_type='form', toolbar=False, submenu=False):
        """Override to apply dynamic domains and attributes"""
        res = super().fields_view_get(view_id=view_id, view_type=view_type, toolbar=toolbar, submenu=submenu)
        if view_type == 'form':
            doc = etree.XML(res['arch'])

            # Set domain for assigned_to field
            for node in doc.xpath("//field[@name='assigned_to']"):
                node.set('domain', str(self._get_it_staff_domain()))

            # Check if user is a manager
            is_manager = self.env.user.has_group('bssic_requests.group_bssic_direct_manager')

            # Set domain for employee_id field based on manager permissions
            employee_domain = self._get_employee_domain()
            # Get the list of employee IDs that match the domain
            employees = self.env['hr.employee'].search(employee_domain)

            # Set domain for employee_id field
            for node in doc.xpath("//field[@name='employee_id']"):
                # Set domain based on user permissions
                if is_manager:
                    # Managers can see employees in their department who report to them
                    node.set('domain', str([('id', 'in', employees.ids)]))
                else:
                    # Regular employees can only see themselves
                    current_user = self.env.user
                    employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)
                    if employee:
                        node.set('domain', str([('id', '=', employee.id)]))
                    else:
                        node.set('domain', str([('id', '=', -1)]))

                # Make the field editable in draft state for all users
                attrs = {'readonly': [('state', '!=', 'draft')]}
                node.set('attrs', str(attrs))

            # Always make request_type_id readonly when a specific request type is selected
            context = self.env.context
            if context.get('default_request_type_code') or context.get('default_request_type_id'):
                for node in doc.xpath("//field[@name='request_type_id']"):
                    node.set('readonly', '1')
                    node.set('force_save', '1')

            res['arch'] = etree.tostring(doc, encoding='unicode')
        return res

    @api.model
    def default_get(self, fields_list):
        """Set default values for new requests"""
        res = super().default_get(fields_list)

        # Get current user's employee record
        current_user = self.env.user
        employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)

        if employee:
            res['employee_id'] = employee.id
            # Also set employee_number if available
            if employee.int_id:
                res['employee_number'] = employee.int_id

        # Check if request_type_code is in context and set request_type_id accordingly
        context = self.env.context
        if context.get('default_request_type_code'):
            request_type_code = context.get('default_request_type_code')
            request_type = self.env['bssic.request.type'].search([('code', '=', request_type_code)], limit=1)
            if request_type:
                res['request_type_id'] = request_type.id
                res['request_type_code'] = request_type_code

        # Set default name if provided in context
        if context.get('default_name'):
            res['name'] = context.get('default_name')

        return res

    @api.model
    def create(self, vals):
        """Override create to set sequence and request type"""
        # If request_type_code is provided but request_type_id is not, set request_type_id
        if vals.get('request_type_code') and not vals.get('request_type_id'):
            request_type = self.env['bssic.request.type'].search([('code', '=', vals.get('request_type_code'))], limit=1)
            if request_type:
                vals['request_type_id'] = request_type.id

        # Set sequence number
        if vals.get('name', _('New')) == _('New'):
            # Get the request type name if available
            request_type_name = ""
            if vals.get('request_type_id'):
                request_type = self.env['bssic.request.type'].browse(vals.get('request_type_id'))
                if request_type:
                    request_type_name = request_type.name

            # Generate sequence
            sequence = self.env['ir.sequence'].next_by_code('bssic.request') or _('New')

            # Set name with request type if available
            if request_type_name:
                vals['name'] = f"{request_type_name} - {sequence}"
            else:
                vals['name'] = sequence

        return super().create(vals)
