from odoo import models, api, _

class BSSICRequest(models.Model):
    """Main BSSIC Request model combining all mixins"""
    _name = 'bssic.request'
    _description = 'BSSIC Request'
    _inherit = [
        'mail.thread', 
        'mail.activity.mixin',
        'bssic.request.fields.mixin',
        'bssic.request.password.reset.mixin',
        'bssic.request.usb.mixin',
        'bssic.request.extension.mixin',
        'bssic.request.permission.mixin',
        'bssic.request.email.mixin',
        'bssic.request.technical.mixin',
        'bssic.request.authorization.delegation.mixin',
        'bssic.request.free.entry.mixin',
        'bssic.request.workflow.mixin',
        'bssic.request.validations.mixin',
        'bssic.request.helpers.mixin',
        'bssic.request.onchange.mixin',
    ]
    _order = 'id desc'

    @api.model
    def _search(self, args, offset=0, limit=None, order=None, count=False, access_rights_uid=None):
        """Override search for IT staff filtering"""
        # If we're searching for employees to assign and the context has 'it_staff_only'
        context = self._context or {}
        if self._name == 'hr.employee' and context.get('it_staff_only'):
            # Apply the IT staff domain filter
            args = args + self.env['bssic.request']._get_it_staff_domain()
        return super(BSSICRequest, self)._search(args, offset, limit, order, count, access_rights_uid)
