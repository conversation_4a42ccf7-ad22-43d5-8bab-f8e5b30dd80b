from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from lxml import etree

class BSSICRequest(models.Model):
    """Main BSSIC Request model - temporarily consolidated for debugging"""
    _name = 'bssic.request'
    _description = 'BSSIC Request'
    _inherit = ['mail.thread', 'mail.activity.mixin', 'bssic.request.fields.part2.mixin', 'bssic.request.methods.mixin', 'bssic.request.validations.mixin']
    _order = 'id desc'

    # Basic fields from request_fields.py
    name = fields.Char('Request Reference', required=True, copy=False, readonly=True,
                       default=lambda self: _('New'))
    employee_id = fields.Many2one('hr.employee', string='Employee', required=True,
                                   tracking=True, ondelete="restrict")
    employee_number = fields.Char(string='Employee Number (ID)', tracking=True,
                                help="Enter employee ID number to automatically fetch employee details")
    department_id = fields.Many2one(related='employee_id.department_id',
                                   string='Department', store=True, readonly=True)
    job_id = fields.Many2one(related='employee_id.job_id', string='Job Position',
                            store=True, readonly=True)
    request_type_id = fields.Many2one('bssic.request.type', string='Request Type',
                                     required=True, tracking=True)
    request_type_code = fields.Char('Request Type Code', tracking=True)
    request_date = fields.Date('Request Date', default=fields.Date.context_today,
                              required=True, tracking=True)
    description = fields.Text('Description', tracking=True)

    # State and workflow fields
    state = fields.Selection([
        ('draft', 'Draft'),
        ('submitted', 'Submitted'),
        ('direct_manager', 'Direct Manager Approval'),
        ('audit_manager', 'Audit Manager Approval'),
        ('it_manager', 'IT Manager Approval'),
        ('assigned', 'Assigned to IT Staff'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('rejected', 'Rejected'),
    ], string='Status', default='draft', tracking=True)

    assigned_to = fields.Many2one('hr.employee', string='Assigned To',
                                 tracking=True, ondelete="set null",
                                 domain="[('department_id.name', 'ilike', 'IT')]")
    rejection_reason = fields.Text('Rejection Reason', tracking=True)
    completion_notes = fields.Text('Completion Notes', tracking=True)

    # Activity Log
    activity_log_ids = fields.One2many('bssic.request.activity.log', 'request_id', string='Activity Log')

    # Technical request fields
    is_technical = fields.Boolean(string='Is Technical Request', default=False)
    is_manager = fields.Boolean(string='Is Manager', compute='_compute_is_manager', store=False)

    # Computed fields for showing different field groups
    show_password_fields = fields.Boolean(
        string='Show Password Fields',
        compute='_compute_show_fields'
    )
    show_usb_fields = fields.Boolean(
        string='Show USB Fields',
        compute='_compute_show_fields'
    )
    show_extension_fields = fields.Boolean(
        string='Show Extension Fields',
        compute='_compute_show_fields'
    )
    show_permission_fields = fields.Boolean(
        string='Show Permission Fields',
        compute='_compute_show_fields'
    )
    show_email_fields = fields.Boolean(
        string='Show Email Fields',
        compute='_compute_show_fields'
    )
    show_technical_fields = fields.Boolean(
        string='Show Technical Fields',
        compute='_compute_show_fields'
    )
    show_authorization_delegation_fields = fields.Boolean(
        string='Show Authorization Delegation Fields',
        compute='_compute_show_fields'
    )
    show_free_entry_fields = fields.Boolean(
        string='Show Free Entry Fields',
        compute='_compute_show_fields'
    )

    # Password reset fields
    username = fields.Char('Username', tracking=True)
    device_type = fields.Selection([
        ('internet', 'Internet'),
        ('system', 'System'),
        ('swift', 'Swift'),
        ('other', 'Other')
    ], string='Device Type', tracking=True)
    request_reason = fields.Selection([
        ('password_reset', 'Password Reset (Forgotten/Unable to login)'),
        ('account_reactivation', 'Account Reactivation (Account/Device locked)')
    ], string='Request Reason', tracking=True)

    # USB fields
    usb_purpose = fields.Char('Purpose of USB Usage', tracking=True)
    usb_duration = fields.Char('Required Duration', tracking=True)
    data_type = fields.Char('Type of Data to Transfer', tracking=True)

    # Extension fields
    extension_duration = fields.Char('Required Extension Period', tracking=True)
    extension_reason = fields.Text('Reason for Extension', tracking=True)

    # Permission fields
    permission_type = fields.Selection([
        ('add', 'Add'),
        ('modify', 'Modify'),
        ('delete', 'Delete'),
        ('withdraw', 'Withdraw'),
        ('activate', 'Activate'),
        ('deactivate', 'Deactivate'),
    ], string='Permission Type', tracking=True)
    user_name = fields.Char('User Name', tracking=True)
    validity_from = fields.Date('Valid From', tracking=True)
    validity_to = fields.Date('Valid To', tracking=True)

    # Department permissions
    accounting_dept = fields.Boolean(string='Accounting Department', tracking=True)
    accounting_level = fields.Selection([
        ('user', 'User'),
        ('clark', 'Clark'),
        ('verifier1', 'Verifier1'),
        ('verifier2', 'Verifier2'),
        ('authorizer', 'Authorizer'),
    ], string='Accounting Level', tracking=True)
    accounting_group = fields.Char(string='Accounting Group', tracking=True)

    internal_audit = fields.Boolean(string='Internal Auditing', tracking=True)
    internal_audit_level = fields.Selection([
        ('user', 'User'),
        ('clark', 'Clark'),
        ('verifier1', 'Verifier1'),
        ('verifier2', 'Verifier2'),
        ('authorizer', 'Authorizer'),
    ], string='Internal Audit Level', tracking=True)
    internal_audit_group = fields.Char(string='Internal Audit Group', tracking=True)

    risk_dept = fields.Boolean(string='Risk', tracking=True)
    risk_level = fields.Selection([
        ('user', 'User'),
        ('clark', 'Clark'),
        ('verifier1', 'Verifier1'),
        ('verifier2', 'Verifier2'),
        ('authorizer', 'Authorizer'),
    ], string='Risk Level', tracking=True)
    risk_group = fields.Char(string='Risk Group', tracking=True)

    @api.depends()
    def _compute_is_manager(self):
        """Check if the current user has direct manager permissions"""
        is_manager = self.env.user.has_group('bssic_requests.group_bssic_direct_manager')
        for record in self:
            record.is_manager = is_manager

    @api.depends('request_type_id')
    def _compute_show_fields(self):
        for record in self:
            record.show_password_fields = record.request_type_id.show_password_fields if record.request_type_id else False
            record.show_usb_fields = record.request_type_id.show_usb_fields if record.request_type_id else False
            record.show_extension_fields = record.request_type_id.show_extension_fields if record.request_type_id else False
            record.show_permission_fields = record.request_type_id.show_permission_fields if record.request_type_id else False
            record.show_email_fields = record.request_type_id.show_email_fields if record.request_type_id else False
            record.show_authorization_delegation_fields = record.request_type_id.show_authorization_delegation_fields if record.request_type_id else False
            record.show_free_entry_fields = record.request_type_id.show_free_entry_fields if record.request_type_id else False
            is_tech = record.request_type_id.code == 'technical' if record.request_type_id else False
            record.show_technical_fields = is_tech
            record.is_technical = is_tech

    @api.model
    def create(self, vals):
        """Override create to set sequence and request type"""
        # If request_type_code is provided but request_type_id is not, set request_type_id
        if vals.get('request_type_code') and not vals.get('request_type_id'):
            request_type = self.env['bssic.request.type'].search([('code', '=', vals.get('request_type_code'))], limit=1)
            if request_type:
                vals['request_type_id'] = request_type.id

        # Set sequence number
        if vals.get('name', _('New')) == _('New'):
            # Get the request type name if available
            request_type_name = ""
            if vals.get('request_type_id'):
                request_type = self.env['bssic.request.type'].browse(vals.get('request_type_id'))
                if request_type:
                    request_type_name = request_type.name

            # Generate sequence
            sequence = self.env['ir.sequence'].next_by_code('bssic.request') or _('New')

            # Set name with request type if available
            if request_type_name:
                vals['name'] = f"{request_type_name} - {sequence}"
            else:
                vals['name'] = sequence

        return super().create(vals)

    @api.model
    def default_get(self, fields_list):
        """Set default values for new requests"""
        res = super().default_get(fields_list)

        # Get current user's employee record
        current_user = self.env.user
        employee = self.env['hr.employee'].search([('user_id', '=', current_user.id)], limit=1)

        if employee:
            res['employee_id'] = employee.id
            # Also set employee_number if available
            if employee.int_id:
                res['employee_number'] = employee.int_id

        # Check if request_type_code is in context and set request_type_id accordingly
        context = self.env.context
        if context.get('default_request_type_code'):
            request_type_code = context.get('default_request_type_code')
            request_type = self.env['bssic.request.type'].search([('code', '=', request_type_code)], limit=1)
            if request_type:
                res['request_type_id'] = request_type.id
                res['request_type_code'] = request_type_code

        # Set default name if provided in context
        if context.get('default_name'):
            res['name'] = context.get('default_name')

        return res

    # Workflow methods
    def action_submit(self):
        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.request', self.id, 'submitted',
                notes=_('Request submitted for approval'),
                old_state='draft', new_state='direct_manager'
            )

        # First set state to submitted
        self.state = 'submitted'
        # Then move to direct manager approval
        self.state = 'direct_manager'
        # Notify direct manager
        direct_manager = self.employee_id.parent_id
        if direct_manager and direct_manager.user_id:
            self.message_subscribe(partner_ids=[direct_manager.user_id.partner_id.id])
            self.message_post(
                body=_('A new request has been submitted for your approval.'),
                partner_ids=[direct_manager.user_id.partner_id.id]
            )

    def action_approve_direct_manager(self):
        # Check if this is a technical request
        if self.is_technical or self.request_type_id.code == 'technical':
            # Log activity
            if self.id:
                self.env['bssic.request.activity.log'].create_activity_log(
                    'bssic.request', self.id, 'direct_manager_approved',
                    notes=_('Approved by Direct Manager (Technical Request - Skip Audit)'),
                    old_state='direct_manager', new_state='it_manager'
                )

            # Skip audit manager approval for technical requests
            self.state = 'it_manager'
            # Find IT manager and notify
            it_manager_group = self.env.ref('bssic_requests.group_bssic_it_manager')
            it_manager_users = it_manager_group.users
            partner_ids = it_manager_users.mapped('partner_id.id')
            if partner_ids:
                self.message_subscribe(partner_ids=partner_ids)
                self.message_post(
                    body=_('This technical request has been approved by the direct manager and requires your approval.'),
                    partner_ids=partner_ids
                )
        else:
            # Log activity
            if self.id:
                self.env['bssic.request.activity.log'].create_activity_log(
                    'bssic.request', self.id, 'direct_manager_approved',
                    notes=_('Approved by Direct Manager'),
                    old_state='direct_manager', new_state='audit_manager'
                )

            # Regular workflow for non-technical requests
            self.state = 'audit_manager'
            # Find audit manager and notify
            audit_group = self.env.ref('bssic_requests.group_bssic_audit_manager')
            audit_users = audit_group.users
            partner_ids = audit_users.mapped('partner_id.id')
            if partner_ids:
                self.message_subscribe(partner_ids=partner_ids)
                self.message_post(
                    body=_('This request has been approved by the direct manager and requires your approval.'),
                    partner_ids=partner_ids
                )

    def action_approve_audit_manager(self):
        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.request', self.id, 'audit_manager_approved',
                notes=_('Approved by Audit Manager'),
                old_state='audit_manager', new_state='it_manager'
            )

        self.state = 'it_manager'
        # Find IT manager and notify
        it_manager_group = self.env.ref('bssic_requests.group_bssic_it_manager')
        it_manager_users = it_manager_group.users
        partner_ids = it_manager_users.mapped('partner_id.id')
        if partner_ids:
            self.message_subscribe(partner_ids=partner_ids)
            self.message_post(
                body=_('This request has been approved by the audit manager and requires your approval.'),
                partner_ids=partner_ids
            )

    def action_approve_it_manager(self):
        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.request', self.id, 'it_manager_approved',
                notes=_('Approved by IT Manager'),
                old_state='it_manager', new_state='assigned'
            )

        self.state = 'assigned'

    def action_assign(self):
        if not self.assigned_to:
            raise UserError(_('Please select an IT staff member to assign this request to.'))

        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.request', self.id, 'assigned',
                notes=_('Assigned to IT Staff: %s') % self.assigned_to.name,
                old_state='assigned', new_state='in_progress',
                assigned_to_id=self.assigned_to.id
            )

        self.state = 'in_progress'
        # Notify assigned staff
        if self.assigned_to.user_id:
            self.message_subscribe(partner_ids=[self.assigned_to.user_id.partner_id.id])
            self.message_post(
                body=_('This request has been assigned to you for implementation.'),
                partner_ids=[self.assigned_to.user_id.partner_id.id]
            )

    def action_complete(self):
        if not self.completion_notes:
            raise UserError(_('Please add completion notes before marking as completed.'))

        # Log activity
        if self.id:
            self.env['bssic.request.activity.log'].create_activity_log(
                'bssic.request', self.id, 'completed',
                notes=_('Request completed. Notes: %s') % self.completion_notes,
                old_state='in_progress', new_state='completed'
            )

        self.state = 'completed'
        # Notify employee
        if self.employee_id.user_id:
            self.message_post(
                body=_('Your request has been completed. Notes: %s') % self.completion_notes,
                partner_ids=[self.employee_id.user_id.partner_id.id]
            )

    def action_reject(self):
        return {
            'name': _('Reject Request'),
            'type': 'ir.actions.act_window',
            'res_model': 'bssic.request.reject.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {'default_request_id': self.id}
        }

    # Note: Helper methods and onchange methods are now in separate mixins
