# BSIC Requests Management

This module provides a comprehensive system for managing various types of requests in BSIC with a multi-stage approval workflow.

## Features

- Multiple request types:
  - Password Reset
  - USB Storage Usage
  - Device Usage Extension
  - Permission Requests
  - Email Requests
  - Technical Support
  - Authorization Delegation
  - Free Entry (القيد الحر)
  - Stationery Requests

- Multi-stage approval workflow:
  1. Employee submits request
  2. Direct Manager approval
  3. Audit Manager approval (skipped for technical requests)
  4. IT Manager approval
  5. Assignment to IT Staff
  6. Implementation
  7. Completion

- Role-based permissions:
  - Employees can create and view their own requests
  - Direct Managers can approve their team's requests
  - Audit Managers can approve requests after Direct Managers
  - IT Managers can approve, assign, and manage all requests
  - IT Staff can implement assigned requests

## Code Organization

The module has been reorganized for better maintainability:

### Models Structure
```
models/
├── base/                    # Core functionality
│   ├── request_fields.py    # Basic field definitions
│   ├── request_workflow.py  # Workflow and approval logic
│   ├── request_validations.py # Validation constraints
│   ├── request_helpers.py   # Helper methods
│   ├── request_onchange.py  # Onchange methods
│   ├── request_main.py      # Main model combining all mixins
│   ├── request_type.py      # Request type model
│   ├── technical_category.py # Technical categories
│   └── request_activity_log.py # Activity logging
├── request_types/           # Request type specific fields
│   ├── password_reset_fields.py
│   ├── usb_fields.py
│   ├── extension_fields.py
│   ├── permission_fields.py
│   ├── email_fields.py
│   ├── technical_fields.py
│   ├── authorization_delegation_fields.py
│   └── free_entry_fields.py
└── wizards/                 # Wizard models
    ├── reject_wizard.py
    └── receipt_confirmation_wizard.py
```

### Views Structure
```
views/
├── base/                    # Core views
│   ├── request_views.xml
│   ├── request_type_views.xml
│   ├── technical_category_views.xml
│   └── request_activity_log_views.xml
├── request_types/           # Request type specific views
│   ├── password_reset_request_views.xml
│   ├── usb_request_views.xml
│   ├── extension_request_views.xml
│   ├── permission_request_views.xml
│   ├── email_request_views.xml
│   ├── technical_request_views.xml
│   ├── authorization_delegation_request_views.xml
│   └── free_entry_request_views.xml
├── wizards/                 # Wizard views
│   └── receipt_confirmation_wizard_view.xml
└── menus/                   # Menu definitions
    ├── menu_views.xml
    └── technical_support_menu.xml
```

- Notifications at each stage of the workflow
- Rejection with reason at any stage
- Integration with employee data using employee ID

## Configuration

1. Assign users to the appropriate security groups:
   - BSSIC Employee
   - BSSIC Direct Manager
   - BSSIC Audit Manager
   - BSSIC IT Manager
   - BSSIC IT Staff

2. Configure request types as needed

## Usage

1. Employee creates a request
2. Request goes through the approval workflow
3. IT staff implements the request
4. Employee receives notification of completion

## Technical Information

- The module integrates with the HR module to use employee data
- Uses the mail module for notifications and chatter
- Custom security groups and record rules for proper access control