from odoo import models, fields

class FreeEntryFieldsMixin(models.AbstractModel):
    """Mixin containing free entry request fields"""
    _name = 'bssic.request.free.entry.mixin'
    _description = 'Free Entry Request Fields Mixin'

    # For free entry request
    free_entry_subject = fields.Char(string='Subject', tracking=True)
    free_entry_details = fields.Text(string='Operation Details', tracking=True)
    free_entry_from_date = fields.Date(string='From Date', tracking=True)
    free_entry_to_date = fields.Date(string='To Date', tracking=True)
    free_entry_user_name = fields.Char(string='User Name', tracking=True)
    free_entry_type = fields.Selection([
        ('free_entry', 'Free Entry'),
        ('reverse_entry', 'Reverse Entry')
    ], string='Entry Type', tracking=True)
